#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
硬件信息检测模块
提供完整的Windows硬件信息检测功能

此模块使用WMI和psutil库来获取详细的硬件信息，
包括CPU、内存、显卡、存储、主板等组件的详细信息。
支持异步检测，避免界面卡顿。
"""

import json
import traceback
import datetime
from typing import Dict, Any, Optional, List
from PySide6.QtCore import QThread, Signal

try:
    import wmi

    WMI_AVAILABLE = True
except ImportError:
    WMI_AVAILABLE = False
    print("警告: WMI模块未安装，部分硬件检测功能将不可用")

try:
    import psutil

    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("警告: psutil模块未安装，部分系统信息将不可用")


def wmi_date_to_str(wmi_date: Optional[str]) -> Optional[str]:
    """
    将WMI CIM_DATETIME格式转换为ISO 8601字符串

    Args:
        wmi_date: WMI日期时间字符串

    Returns:
        str: ISO 8601格式的日期时间字符串，如果转换失败返回原字符串
    """
    if wmi_date is None:
        return None
    try:
        # 格式: yyyymmddhhmmss.ffffff+|-zzz
        dt = datetime.datetime.strptime(wmi_date.split(".")[0], "%Y%m%d%H%M%S")
        return dt.isoformat()
    except (ValueError, IndexError):
        return str(wmi_date)


def safe_wmi_operation(operation_func, default_value=None):
    """
    安全执行WMI操作的装饰器函数

    Args:
        operation_func: 要执行的WMI操作函数
        default_value: 操作失败时的默认返回值

    Returns:
        操作结果或默认值
    """
    try:
        if not WMI_AVAILABLE:
            return {"error": "WMI模块不可用", "data": default_value}
        return operation_func()
    except Exception as e:
        return {"error": str(e), "traceback": traceback.format_exc()}


def safe_get_wmi_property(wmi_object, property_name, default_value="未知"):
    """
    安全获取WMI对象属性

    Args:
        wmi_object: WMI对象
        property_name: 属性名
        default_value: 默认值

    Returns:
        属性值或默认值
    """
    try:
        value = getattr(wmi_object, property_name, None)
        if value is None:
            return default_value
        return value
    except (AttributeError, Exception):
        return default_value


class HardwareDetector:
    """硬件信息检测器"""

    def __init__(self):
        """初始化硬件检测器"""
        self.wmi_connection = None
        self._init_wmi_connection()

    def _init_wmi_connection(self) -> None:
        """初始化WMI连接"""
        if WMI_AVAILABLE:
            try:
                self.wmi_connection = wmi.WMI()
            except Exception as e:
                print(f"WMI连接初始化失败: {e}")
                self.wmi_connection = None

    def get_system_info(self) -> Dict[str, Any]:
        """获取操作系统和基本系统信息"""

        def _get_system_info():
            if not self.wmi_connection:
                return {"error": "WMI连接不可用"}

            os_info = self.wmi_connection.Win32_OperatingSystem()[0]
            computer_system = self.wmi_connection.Win32_ComputerSystem()[0]

            return {
                "caption": os_info.Caption,
                "version": os_info.Version,
                "build_number": os_info.BuildNumber,
                "os_architecture": os_info.OSArchitecture,
                "hostname": os_info.CSName,
                "total_visible_memory": f"{int(os_info.TotalVisibleMemorySize) / 1024:.2f} MB",
                "free_physical_memory": f"{int(os_info.FreePhysicalMemory) / 1024:.2f} MB",
                "computer_manufacturer": computer_system.Manufacturer,
                "computer_model": computer_system.Model,
                "total_physical_memory": (
                    f"{int(computer_system.TotalPhysicalMemory) / (1024**3):.2f} GB"
                    if computer_system.TotalPhysicalMemory
                    else "未知"
                ),
            }

        return safe_wmi_operation(_get_system_info, {})

    def get_cpu_info(self) -> Dict[str, Any]:
        """获取CPU详细信息"""

        def _get_cpu_info():
            if not self.wmi_connection:
                return {"error": "WMI连接不可用"}

            processors = []
            for p in self.wmi_connection.Win32_Processor():
                processor_info = {
                    "device_id": safe_get_wmi_property(p, "DeviceID"),
                    "name": (
                        safe_get_wmi_property(p, "Name", "未知").strip()
                        if safe_get_wmi_property(p, "Name") != "未知"
                        else "未知"
                    ),
                    "manufacturer": safe_get_wmi_property(p, "Manufacturer"),
                    "current_clock_speed": (
                        f"{safe_get_wmi_property(p, 'CurrentClockSpeed', 0)} MHz"
                        if safe_get_wmi_property(p, "CurrentClockSpeed", 0)
                        else "未知"
                    ),
                    "max_clock_speed": (
                        f"{safe_get_wmi_property(p, 'MaxClockSpeed', 0)} MHz"
                        if safe_get_wmi_property(p, "MaxClockSpeed", 0)
                        else "未知"
                    ),
                    "number_of_cores": safe_get_wmi_property(p, "NumberOfCores"),
                    "number_of_logical_processors": safe_get_wmi_property(
                        p, "NumberOfLogicalProcessors"
                    ),
                    "architecture": safe_get_wmi_property(p, "Architecture"),
                    "family": safe_get_wmi_property(p, "Family"),
                    "model": safe_get_wmi_property(p, "Model"),
                    "stepping": safe_get_wmi_property(p, "Stepping"),
                    "processor_id": safe_get_wmi_property(p, "ProcessorId"),
                    "socket_designation": safe_get_wmi_property(p, "SocketDesignation"),
                }
                processors.append(processor_info)

            # 添加psutil的CPU信息
            cpu_additional = {}
            if PSUTIL_AVAILABLE:
                try:
                    cpu_additional.update(
                        {
                            "cpu_count_physical": psutil.cpu_count(logical=False),
                            "cpu_count_logical": psutil.cpu_count(logical=True),
                            "cpu_freq_current": (
                                f"{psutil.cpu_freq().current:.2f} MHz"
                                if psutil.cpu_freq()
                                else "未知"
                            ),
                            "cpu_freq_min": (
                                f"{psutil.cpu_freq().min:.2f} MHz"
                                if psutil.cpu_freq() and psutil.cpu_freq().min
                                else "未知"
                            ),
                            "cpu_freq_max": (
                                f"{psutil.cpu_freq().max:.2f} MHz"
                                if psutil.cpu_freq() and psutil.cpu_freq().max
                                else "未知"
                            ),
                        }
                    )
                except Exception as e:
                    cpu_additional["psutil_error"] = str(e)

            return {"wmi_processor_info": processors, "additional_info": cpu_additional}

        return safe_wmi_operation(
            _get_cpu_info, {"wmi_processor_info": [], "additional_info": {}}
        )

    def get_memory_info(self) -> Dict[str, Any]:
        """获取内存信息"""

        def _get_memory_info():
            if not self.wmi_connection:
                return {"error": "WMI连接不可用"}

            physical_memory = []
            for mem in self.wmi_connection.Win32_PhysicalMemory():
                memory_info = {
                    "capacity": f"{int(mem.Capacity) / (1024**3):.2f} GB",
                    "part_number": mem.PartNumber.strip() if mem.PartNumber else "未知",
                    "speed": f"{mem.Speed} MHz" if mem.Speed else "未知",
                    "manufacturer": mem.Manufacturer if mem.Manufacturer else "未知",
                    "device_locator": (
                        mem.DeviceLocator if mem.DeviceLocator else "未知"
                    ),
                    "bank_label": mem.BankLabel if mem.BankLabel else "未知",
                    "memory_type": mem.MemoryType if mem.MemoryType else "未知",
                    "form_factor": mem.FormFactor if mem.FormFactor else "未知",
                }
                physical_memory.append(memory_info)

            # 添加psutil的内存信息
            memory_additional = {}
            if PSUTIL_AVAILABLE:
                try:
                    virtual_mem = psutil.virtual_memory()
                    swap_mem = psutil.swap_memory()
                    memory_additional.update(
                        {
                            "total_memory": f"{virtual_mem.total / (1024**3):.2f} GB",
                            "available_memory": f"{virtual_mem.available / (1024**3):.2f} GB",
                            "used_memory": f"{virtual_mem.used / (1024**3):.2f} GB",
                            "memory_percent": f"{virtual_mem.percent:.1f}%",
                            "swap_total": f"{swap_mem.total / (1024**3):.2f} GB",
                            "swap_used": f"{swap_mem.used / (1024**3):.2f} GB",
                            "swap_percent": f"{swap_mem.percent:.1f}%",
                        }
                    )
                except Exception as e:
                    memory_additional["psutil_error"] = str(e)

            return {
                "physical_slots": physical_memory,
                "additional_info": memory_additional,
            }

        return safe_wmi_operation(
            _get_memory_info, {"physical_slots": [], "additional_info": {}}
        )

    def get_disk_info(self) -> Dict[str, Any]:
        """获取磁盘信息"""

        def _get_disk_info():
            if not self.wmi_connection:
                return {"error": "WMI连接不可用"}

            physical_disks = []
            for disk in self.wmi_connection.Win32_DiskDrive():
                disk_info = {
                    "model": disk.Model if disk.Model else "未知",
                    "size": (
                        f"{int(disk.Size) / (1024**3):.2f} GB" if disk.Size else "未知"
                    ),
                    "interface_type": (
                        disk.InterfaceType if disk.InterfaceType else "未知"
                    ),
                    "media_type": disk.MediaType if disk.MediaType else "未知",
                    "serial_number": (
                        disk.SerialNumber.strip() if disk.SerialNumber else "未知"
                    ),
                    "firmware_revision": (
                        disk.FirmwareRevision if disk.FirmwareRevision else "未知"
                    ),
                }
                physical_disks.append(disk_info)

            # 添加逻辑磁盘信息
            logical_disks = []
            for disk in self.wmi_connection.Win32_LogicalDisk():
                if disk.DriveType == 3:  # 本地磁盘
                    logical_info = {
                        "device_id": disk.DeviceID,
                        "file_system": disk.FileSystem if disk.FileSystem else "未知",
                        "size": (
                            f"{int(disk.Size) / (1024**3):.2f} GB"
                            if disk.Size
                            else "未知"
                        ),
                        "free_space": (
                            f"{int(disk.FreeSpace) / (1024**3):.2f} GB"
                            if disk.FreeSpace
                            else "未知"
                        ),
                        "used_space": (
                            f"{(int(disk.Size) - int(disk.FreeSpace)) / (1024**3):.2f} GB"
                            if disk.Size and disk.FreeSpace
                            else "未知"
                        ),
                        "volume_name": disk.VolumeName if disk.VolumeName else "未命名",
                    }
                    logical_disks.append(logical_info)

            # 添加psutil的磁盘信息
            disk_additional = {}
            if PSUTIL_AVAILABLE:
                try:
                    disk_usage = {}
                    disk_io = psutil.disk_io_counters()
                    for partition in psutil.disk_partitions():
                        try:
                            usage = psutil.disk_usage(partition.mountpoint)
                            disk_usage[partition.device] = {
                                "total": f"{usage.total / (1024**3):.2f} GB",
                                "used": f"{usage.used / (1024**3):.2f} GB",
                                "free": f"{usage.free / (1024**3):.2f} GB",
                                "percent": f"{(usage.used / usage.total * 100):.1f}%",
                            }
                        except PermissionError:
                            continue

                    disk_additional.update(
                        {
                            "disk_usage": disk_usage,
                            "disk_io_read_bytes": (
                                f"{disk_io.read_bytes / (1024**3):.2f} GB"
                                if disk_io
                                else "未知"
                            ),
                            "disk_io_write_bytes": (
                                f"{disk_io.write_bytes / (1024**3):.2f} GB"
                                if disk_io
                                else "未知"
                            ),
                        }
                    )
                except Exception as e:
                    disk_additional["psutil_error"] = str(e)

            return {
                "physical_disks": physical_disks,
                "logical_disks": logical_disks,
                "additional_info": disk_additional,
            }

        return safe_wmi_operation(
            _get_disk_info,
            {"physical_disks": [], "logical_disks": [], "additional_info": {}},
        )

    def get_gpu_info(self) -> Dict[str, Any]:
        """获取GPU信息"""

        def _get_gpu_info():
            if not self.wmi_connection:
                return {"error": "WMI连接不可用"}

            gpus = []
            for gpu in self.wmi_connection.Win32_VideoController():
                pnp_id = gpu.PNPDeviceID
                if pnp_id and "PCI" in pnp_id:
                    gpu_info = {
                        "name": gpu.Name if gpu.Name else "未知",
                        "pnp_device_id": pnp_id,
                        "driver_version": (
                            gpu.DriverVersion if gpu.DriverVersion else "未知"
                        ),
                        "driver_date": (
                            wmi_date_to_str(gpu.DriverDate)
                            if gpu.DriverDate
                            else "未知"
                        ),
                        "current_refresh_rate": (
                            f"{gpu.CurrentRefreshRate} Hz"
                            if gpu.CurrentRefreshRate
                            else "未知"
                        ),
                        "current_horizontal_resolution": gpu.CurrentHorizontalResolution,
                        "current_vertical_resolution": gpu.CurrentVerticalResolution,
                        "video_memory": (
                            f"{int(gpu.AdapterRAM) / (1024**3):.2f} GB"
                            if gpu.AdapterRAM
                            else "未知"
                        ),
                        "video_processor": (
                            gpu.VideoProcessor if gpu.VideoProcessor else "未知"
                        ),
                        "adapter_compatibility": (
                            gpu.AdapterCompatibility
                            if gpu.AdapterCompatibility
                            else "未知"
                        ),
                    }
                    gpus.append(gpu_info)

            return {"gpus": gpus}

        return safe_wmi_operation(_get_gpu_info, {"gpus": []})

    def get_motherboard_info(self) -> Dict[str, Any]:
        """获取主板和BIOS信息"""

        def _get_motherboard_info():
            if not self.wmi_connection:
                return {"error": "WMI连接不可用"}

            baseboard = self.wmi_connection.Win32_BaseBoard()[0]
            bios = self.wmi_connection.Win32_BIOS()[0]

            return {
                "motherboard": {
                    "manufacturer": (
                        baseboard.Manufacturer if baseboard.Manufacturer else "未知"
                    ),
                    "product": baseboard.Product if baseboard.Product else "未知",
                    "version": baseboard.Version if baseboard.Version else "未知",
                    "serial_number": (
                        baseboard.SerialNumber if baseboard.SerialNumber else "未知"
                    ),
                },
                "bios": {
                    "manufacturer": bios.Manufacturer if bios.Manufacturer else "未知",
                    "version": (
                        bios.SMBIOSBIOSVersion if bios.SMBIOSBIOSVersion else "未知"
                    ),
                    "release_date": (
                        wmi_date_to_str(bios.ReleaseDate)
                        if bios.ReleaseDate
                        else "未知"
                    ),
                    "serial_number": bios.SerialNumber if bios.SerialNumber else "未知",
                    "smbios_version": (
                        bios.SMBIOSMajorVersion if bios.SMBIOSMajorVersion else "未知"
                    ),
                },
            }

        return safe_wmi_operation(
            _get_motherboard_info, {"motherboard": {}, "bios": {}}
        )

    def get_all_hardware_info(self) -> Dict[str, Any]:
        """获取所有硬件信息"""
        hardware_info = {
            "detection_time": datetime.datetime.now().isoformat(),
            "system": self.get_system_info(),
            "cpu": self.get_cpu_info(),
            "memory": self.get_memory_info(),
            "disk": self.get_disk_info(),
            "gpu": self.get_gpu_info(),
            "motherboard": self.get_motherboard_info(),
        }

        return hardware_info


class HardwareDetectionThread(QThread):
    """硬件检测异步线程"""

    # 信号定义
    hardware_detected = Signal(dict)  # 硬件检测完成
    progress_updated = Signal(str, int)  # 进度更新 (状态, 进度百分比)
    error_occurred = Signal(str)  # 错误发生

    def __init__(self, parent=None):
        super().__init__(parent)
        self.detector = HardwareDetector()

    def run(self):
        """执行硬件检测"""
        try:
            self.progress_updated.emit("开始硬件检测...", 0)

            self.progress_updated.emit("正在检测系统信息...", 10)
            system_info = self.detector.get_system_info()

            self.progress_updated.emit("正在检测CPU信息...", 25)
            cpu_info = self.detector.get_cpu_info()

            self.progress_updated.emit("正在检测内存信息...", 40)
            memory_info = self.detector.get_memory_info()

            self.progress_updated.emit("正在检测存储信息...", 55)
            disk_info = self.detector.get_disk_info()

            self.progress_updated.emit("正在检测显卡信息...", 70)
            gpu_info = self.detector.get_gpu_info()

            self.progress_updated.emit("正在检测主板信息...", 85)
            motherboard_info = self.detector.get_motherboard_info()

            self.progress_updated.emit("整理检测结果...", 95)

            # 组装完整的硬件信息
            hardware_data = {
                "detection_time": datetime.datetime.now().isoformat(),
                "system": system_info,
                "cpu": cpu_info,
                "memory": memory_info,
                "disk": disk_info,
                "gpu": gpu_info,
                "motherboard": motherboard_info,
            }

            self.progress_updated.emit("硬件检测完成", 100)
            self.hardware_detected.emit(hardware_data)

        except Exception as e:
            error_msg = f"硬件检测失败: {str(e)}"
            self.error_occurred.emit(error_msg)


if __name__ == "__main__":
    # 测试代码
    print("硬件检测模块测试")
    print("-" * 50)

    detector = HardwareDetector()

    print("检测系统信息...")
    system_info = detector.get_system_info()
    print(f"系统: {system_info.get('caption', '未知')}")

    print("\n检测CPU信息...")
    cpu_info = detector.get_cpu_info()
    if "wmi_processor_info" in cpu_info and cpu_info["wmi_processor_info"]:
        print(f"CPU: {cpu_info['wmi_processor_info'][0].get('name', '未知')}")

    print("\n检测内存信息...")
    memory_info = detector.get_memory_info()
    if "physical_slots" in memory_info:
        total_memory = sum(
            float(slot["capacity"].split()[0])
            for slot in memory_info["physical_slots"]
            if "capacity" in slot
        )
        print(f"内存: {total_memory:.2f} GB")

    print("\n✓ 硬件检测模块测试完成")

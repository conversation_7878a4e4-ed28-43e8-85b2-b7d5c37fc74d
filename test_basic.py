#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基础架构测试
测试管理员权限检查和基本窗口创建
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.admin_check import require_admin, get_admin_status_info


class SimpleTestWindow(QMainWindow):
    """简单测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("Windows硬件工具箱 - 基础架构测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("🔧 Windows硬件工具箱")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("font-size: 24px; font-weight: bold; margin: 20px;")
        
        # 版本信息
        version = QLabel("版本 1.0.0 - 基础架构测试")
        version.setAlignment(Qt.AlignmentFlag.AlignCenter)
        version.setStyleSheet("font-size: 14px; color: gray; margin: 10px;")
        
        # 权限状态
        admin_status = get_admin_status_info()
        status_text = f"管理员权限: {'✅ 已获取' if admin_status['is_admin'] else '❌ 未获取'}\n"
        status_text += f"当前用户: {admin_status['user_name']}\n"
        status_text += f"完整性级别: {admin_status['integrity_level']}"
        
        status_label = QLabel(status_text)
        status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        status_label.setStyleSheet("font-size: 12px; margin: 20px; padding: 20px; border: 1px solid gray; border-radius: 5px;")
        
        # 测试信息
        test_info = QLabel(
            "✅ 基础架构测试成功！\n\n"
            "已完成的功能：\n"
            "• 管理员权限检查\n"
            "• 应用程序初始化\n"
            "• 基础窗口创建\n"
            "• 配置系统\n\n"
            "下一步：开始阶段2 - 硬件信息模块开发"
        )
        test_info.setAlignment(Qt.AlignmentFlag.AlignCenter)
        test_info.setStyleSheet("font-size: 12px; margin: 20px;")
        
        # 添加到布局
        layout.addWidget(title)
        layout.addWidget(version)
        layout.addWidget(status_label)
        layout.addStretch()
        layout.addWidget(test_info)
        layout.addStretch()


def main():
    """主函数"""
    try:
        # 检查管理员权限
        print("检查管理员权限...")
        require_admin()
        print("✅ 管理员权限检查通过")
        
        # 创建应用程序
        print("初始化应用程序...")
        app = QApplication(sys.argv)
        app.setApplicationName("Windows硬件工具箱")
        app.setApplicationVersion("1.0.0")
        
        # 设置字体
        font = QFont("Segoe UI", 9)
        app.setFont(font)
        print("✅ 应用程序初始化完成")
        
        # 创建测试窗口
        print("创建测试窗口...")
        window = SimpleTestWindow()
        window.show()
        print("✅ 测试窗口创建成功")
        
        print("🚀 基础架构测试启动成功！")
        
        # 运行应用程序
        sys.exit(app.exec())
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
        sys.exit(0)
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()

### 项目概述
使用PySide6+PySide6-Fluent-Widgets开发Windows硬件工具箱，遵循Microsoft Fluent Design设计规范和Windows 11设计原则。

### 技术栈要求
- **核心框架**: PySide6 + PySide6-Fluent-Widgets
- **设计规范**: Microsoft Fluent Design + Windows 11设计原则
- **参考文档**: https://learn.microsoft.com/zh-cn/windows/apps/design/
- **权限要求**: 必须以管理员权限运行
- **打包要求**: 使用PyInstaller或cx_Freeze，排除OCTools文件夹

### 项目结构规划
```

├── main.py                     # 主程序入口
├── splash.py                   # 启动动画
├── config/
│   ├── __init__.py
│   ├── settings.py             # 配置管理
│   └── constants.py            # 常量定义
├── ui/
│   ├── __init__.py
│   ├── main_window.py          # 主窗口
│   ├── components/             # 公共组件
│   └── views/                  # 各个页面视图
│       ├── hardware_info.py
│       ├── optimization.py
│       ├── preinstalled_apps.py
│       ├── overclock_tools.py
│       ├── quick_tools.py
│       └── settings_about.py
├── core/
│   ├── __init__.py
│   ├── hardware_detection.py   # 硬件信息获取
│   ├── powershell_commands.py  # PowerShell命令
│   ├── registry_commands.py    # 注册表操作
│   ├── system_cleanup.py       # 系统清理
│   ├── appx_packages.py        # 预装应用管理
│   ├── onedrive_cleanup.py     # OneDrive清理
│   └── quick_tools.py          # 快捷工具定义
├── utils/
│   ├── __init__.py
│   ├── admin_check.py          # 管理员权限检查
│   ├── icon_extractor.py       # 图标提取工具
│   ├── process_utils.py        # 进程管理工具
│   └── file_utils.py           # 文件操作工具
├── assets/
│   ├── icon.ico                # 应用图标
│   ├── alipay.png             # 支付宝二维码
│   └── wechat_pay.png         # 微信支付二维码
├── OCTools/                    # 超频工具目录（打包时排除）
└── requirements.txt            # 依赖列表
```

### 界面设计要求

#### 全局布局
- **主布局**: 左侧导航栏 + 右侧内容区域
- **响应式**: 全局自适应布局
- **主题系统**: 支持明暗主题切换
- **字体**: 使用系统默认字体（Segoe UI）
- **色彩**: 遵循Windows 11 Fluent Design色彩规范

#### 启动动画设计
- **检查管理员权限**: 非管理员模式显示提醒对话框并退出
- **加载内容**: 应用图标、软件名称、版本号、版权信息
- **背景任务**: 预加载硬件信息检测
- **动画效果**: 图标渐入、文字逐行显示、进度条加载
- **持续时间**: 3-5秒或硬件信息加载完成

#### 左侧导航栏
- **导航项目**:
  1. 硬件信息 (Hardware Info)
  2. 优化清理 (Optimization)
  3. 预装应用 (Preinstalled Apps)
  4. 超频工具 (Overclock Tools)
  5. 快捷工具 (Quick Tools)
  6. 设置关于 (Settings & About)
- **交互效果**: Hover悬浮效果、选中状态高亮
- **图标**: 每个导航项配备相应图标

#### 右侧内容区域通用要求
- **页面头部**: 标题 + 页面描述文字
- **内容区域**: 根据功能特点设计布局
- **操作按钮**: 统一的按钮样式和交互反馈
- **状态反馈**: 操作进度、成功/失败状态显示

### 详细功能规范

#### 1. 硬件信息视图
**数据来源**: 必须参照`部分思路解析.md`文件中的硬件信息获取方案，并且实际界面中的硬件信息只需要使用该文档中写出的json数据，并将json字段的键转换成中文的翻译，因为只提供中文用户使用
**界面布局**: 
- 分类卡片式展示（CPU、主板、内存、显卡、硬盘等）
- 每个硬件类别独立卡片
- 信息以键值对形式展示
- 支持信息复制和导出功能
- 界面要求简洁、直观，一眼即可查看所有硬件信息，不需要折叠和滚动

#### 2. 优化清理视图
**功能特性**:
- **全局操作**: 界面顶部全选/全不选按钮（默认全不选）
- **批量执行**: 一键执行所有选中任务
- **单独执行**: 每项任务独立执行按钮
- **执行后处理**: 重启文件资源管理器 + 删除iconcache.db

**选项卡设计**:
1. **PowerShell优化**
   - 设置执行策略为Bypass (`powershell_commands.py` - `EXECUTION_POLICY_COMMAND`)
   - 解锁电源高级选项 (`powershell_commands.py` - `UNLOCK_POWER_COMMAND`)

2. **注册表优化** (`registry_commands.py`)
   - 树形结构展示父子关系
   - 示例: 任务栏相关设置(父) → 任务栏始终精确到秒(子)
   - 多条命令任务: 任一成功即视为执行成功

3. **系统清理** (`system_cleanup.py`)
   - 清理项目列表:
     * 系统临时文件/目录
     * 预读取文件
     * 用户临时文件
     * IE缓存/Web缓存
     * 系统日志文件
     * 系统回收站
     * 磁盘清理工具
     * 系统更新缓存
     * 缩略图缓存
     * DNS缓存

#### 3. 预装应用视图
**功能特性**:
- **全局操作**: 界面顶部全选/全不选按钮（默认全不选）
- **批量卸载**: 一键卸载所有选中应用
- **单独卸载**: 每个应用独立卸载按钮
- **卸载逻辑**: 通配符查找 → 卸载预装包 → 卸载用户安装包

**选项卡设计** (`appx_packages.py`):
1. Windows Xbox相关应用
2. Windows Store商店应用
3. Windows 音视频编解码器应用
4. Windows 系统预装应用
5. OneDrive完整清理 (`onedrive_cleanup.py`)
   - 停止OneDrive进程
   - 卸载UWP和传统应用
   - 清理文件夹和注册表
   - 禁用文件资源管理器集成
   - 清理启动项

#### 4. 超频工具视图
**功能特性**:
- **一键BIOS**: 进入BIOS按钮（需二次确认）
- **工具扫描**: 自动扫描OCTools文件夹
- **文件夹检查**: 不存在则自动创建（适配开发/打包环境）
- **工具识别**: 文件夹名作为工具名称
- **图标提取**: 从可执行文件提取图标（参考`部分思路解析.md`）
- **启动检测**: 区分GUI/控制台程序并适配启动方式
- **执行优先级**: 
  1. 优先同名可执行文件
  2. 查找其他可执行文件
  3. 均以管理员权限运行

**界面布局**: 工具卡片网格布局，每个卡片包含图标、名称、启动按钮

#### 5. 快捷工具视图
**数据来源**: `quick_tools.py`的Python字典
**分类展示**: 根据工具类型进行分类展示
**二次确认工具**: 安全模式、重启、关机、睡眠、锁定计算机
**安全模式**: 一次性进入，重启后自动恢复正常模式
**界面布局**: 分类卡片式布局，每类工具独立区域，但不需要折叠滚动

#### 6. 设置关于视图
**布局参考**: Windows 11设置应用布局标准
**内容要素**:
- 应用信息区域:
  * 应用图标 (`assets/icon.ico`)
  * 应用名称
  * 版本号
  * 版权信息

**功能按钮**:
- **检查更新**: 版本检查和更新功能
- **赞助作者**: 弹出对话框，双选项卡切换支付宝/微信二维码
- **抖音主页**: 跳转作者抖音主页
- **官方Q群**: 跳转QQ群链接

### 开发注意事项
1. **权限管理**: 启动时强制检查管理员权限
2. **异常处理**: 完善的错误处理和用户反馈机制
4. **性能优化**: 硬件信息异步加载，耗时操作异步执行，避免界面卡顿
5. **用户体验**: 所有操作提供实时进度反馈
6. **打包配置**: OCTools文件夹排除，资源文件正确打包

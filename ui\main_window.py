#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
主窗口模块
实现应用程序的主窗口和导航系统

基于MSFluentWindow构建，提供现代化的Fluent Design界面
包含左侧导航栏和右侧内容区域的布局
"""

import os
import sys
from typing import Optional

from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QIcon
from PySide6.QtWidgets import QApplication

from qfluentwidgets import (
    MSFluentWindow,
    NavigationItemPosition,
    SystemThemeListener,
    setTheme,
    Theme,
    isDarkTheme,
)
from qfluentwidgets import FluentIcon as FIF

# 功能视图将在create_sub_interfaces中延迟导入


class MainWindow(MSFluentWindow):
    """
    主窗口类

    继承自MSFluentWindow，实现应用程序的主界面
    包含导航系统和各个功能模块的集成
    """

    def __init__(self):
        """初始化主窗口"""
        super().__init__()

        # 初始化主题监听器
        self.theme_listener: Optional[SystemThemeListener] = None

        # 创建各个功能界面
        self.create_sub_interfaces()

        # 初始化导航系统
        self.init_navigation()

        # 初始化窗口设置
        self.init_window()

        # 初始化主题系统
        self.init_theme_system()

    def create_sub_interfaces(self) -> None:
        """创建各个功能子界面"""
        try:
            # 延迟导入，避免在QApplication创建前导入
            from ui.views.hardware_info import HardwareInfoView
            from ui.views.optimization import OptimizationView
            from ui.views.preinstalled_apps import PreinstalledAppsView
            from ui.views.overclock_tools import OverclockToolsView
            from ui.views.quick_tools import QuickToolsView
            from ui.views.settings_about import SettingsAboutView

            # 硬件信息界面
            self.hardware_interface = HardwareInfoView(self)

            # 优化清理界面
            self.optimization_interface = OptimizationView(self)

            # 预装应用界面
            self.preinstalled_apps_interface = PreinstalledAppsView(self)

            # 超频工具界面
            self.overclock_tools_interface = OverclockToolsView(self)

            # 快捷工具界面
            self.quick_tools_interface = QuickToolsView(self)

            # 设置关于界面
            self.settings_about_interface = SettingsAboutView(self)

        except Exception as e:
            print(f"创建子界面失败: {e}")
            # 这里可以添加错误处理逻辑

    def init_navigation(self) -> None:
        """初始化导航系统"""
        try:
            # 添加主要功能界面到导航栏
            self.addSubInterface(self.hardware_interface, FIF.IOT, "硬件信息", FIF.IOT)

            self.addSubInterface(
                self.optimization_interface, FIF.BROOM, "优化清理", FIF.BROOM
            )

            self.addSubInterface(
                self.preinstalled_apps_interface,
                FIF.APPLICATION,
                "预装应用",
                FIF.APPLICATION,
            )

            self.addSubInterface(
                self.overclock_tools_interface,
                FIF.SPEED_HIGH,
                "超频工具",
                FIF.SPEED_HIGH,
            )

            self.addSubInterface(
                self.quick_tools_interface, FIF.QUICK_NOTE, "快捷工具", FIF.QUICK_NOTE
            )

            # 设置关于界面放在底部
            self.addSubInterface(
                self.settings_about_interface,
                FIF.SETTING,
                "设置关于",
                FIF.SETTING,
                NavigationItemPosition.BOTTOM,
            )

            # 设置导航栏属性
            self.navigationInterface.setExpandWidth(280)
            self.navigationInterface.setMinimumExpandWidth(900)

            # 设置默认选中的界面
            self.navigationInterface.setCurrentItem(
                self.hardware_interface.objectName()
            )

        except Exception as e:
            print(f"初始化导航系统失败: {e}")

    def init_window(self) -> None:
        """初始化窗口设置"""
        try:
            # 设置窗口大小和位置
            self.resize(1200, 800)

            # 设置窗口图标
            icon_path = self.get_icon_path()
            if icon_path and os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))

            # 设置窗口标题
            self.setWindowTitle("Windows硬件工具箱")

            # 居中显示窗口
            self.center_window()

            # 设置最小窗口大小
            self.setMinimumSize(1000, 700)

        except Exception as e:
            print(f"初始化窗口设置失败: {e}")

    def init_theme_system(self) -> None:
        """初始化主题系统"""
        try:
            # 创建主题监听器，自动跟随系统主题
            self.theme_listener = SystemThemeListener(self)
            self.theme_listener.start()

        except Exception as e:
            print(f"初始化主题系统失败: {e}")

    def get_icon_path(self) -> Optional[str]:
        """获取应用程序图标路径"""
        try:
            # 获取项目根目录
            if getattr(sys, "frozen", False):
                # 打包后的可执行文件
                base_path = sys._MEIPASS
            else:
                # 开发环境
                base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

            icon_path = os.path.join(base_path, "assets", "icon.ico")
            return icon_path if os.path.exists(icon_path) else None

        except Exception as e:
            print(f"获取图标路径失败: {e}")
            return None

    def center_window(self) -> None:
        """将窗口居中显示"""
        try:
            # 获取屏幕几何信息
            screen = QApplication.primaryScreen()
            if screen:
                screen_geometry = screen.availableGeometry()
                window_geometry = self.frameGeometry()

                # 计算居中位置
                center_point = screen_geometry.center()
                window_geometry.moveCenter(center_point)

                # 移动窗口到居中位置
                self.move(window_geometry.topLeft())

        except Exception as e:
            print(f"窗口居中失败: {e}")

    def closeEvent(self, event) -> None:
        """窗口关闭事件处理"""
        try:
            # 停止主题监听器
            if self.theme_listener:
                self.theme_listener.terminate()
                self.theme_listener.deleteLater()

            # 调用父类的关闭事件
            super().closeEvent(event)

        except Exception as e:
            print(f"窗口关闭处理失败: {e}")
            event.accept()  # 确保窗口能够关闭

    def switch_to_interface(self, interface_name: str) -> None:
        """切换到指定的界面"""
        try:
            interface_map = {
                "hardware": self.hardware_interface,
                "optimization": self.optimization_interface,
                "preinstalled_apps": self.preinstalled_apps_interface,
                "overclock_tools": self.overclock_tools_interface,
                "quick_tools": self.quick_tools_interface,
                "settings_about": self.settings_about_interface,
            }

            interface = interface_map.get(interface_name)
            if interface:
                self.switchTo(interface)
                self.navigationInterface.setCurrentItem(interface.objectName())
            else:
                print(f"未找到界面: {interface_name}")

        except Exception as e:
            print(f"切换界面失败: {e}")

    def get_current_interface_name(self) -> str:
        """获取当前显示的界面名称"""
        try:
            current_widget = self.stackedWidget.currentWidget()
            if current_widget:
                return current_widget.objectName()
            return "unknown"

        except Exception as e:
            print(f"获取当前界面名称失败: {e}")
            return "error"


def create_main_window() -> MainWindow:
    """
    创建主窗口实例

    这是一个工厂函数，用于创建和配置主窗口

    Returns:
        MainWindow: 配置好的主窗口实例
    """
    try:
        # 确保QApplication已经存在
        app = QApplication.instance()
        if app is None:
            raise RuntimeError("QApplication must be created before MainWindow")

        window = MainWindow()
        return window

    except Exception as e:
        print(f"创建主窗口失败: {e}")
        raise


if __name__ == "__main__":
    # 测试代码
    app = QApplication(sys.argv)

    try:
        window = create_main_window()
        window.show()

        sys.exit(app.exec())

    except Exception as e:
        print(f"应用程序启动失败: {e}")
        sys.exit(1)

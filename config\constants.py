#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
应用程序常量定义模块
定义应用程序中使用的各种常量
"""

import os
from pathlib import Path

# 应用程序信息
APP_NAME = "Windows硬件工具箱"
APP_VERSION = "1.0.0"
APP_AUTHOR = "@抖音小念（凡尘超频团队）"
APP_DESCRIPTION = "一个基于PySide6和Fluent Design的现代化Windows系统工具"

# 版权信息
COPYRIGHT_TEXT = f"© 2025 {APP_AUTHOR}. 保留所有权利."

# 项目路径
PROJECT_ROOT = Path(__file__).parent.parent
ASSETS_DIR = PROJECT_ROOT / "assets"
DOCS_DIR = PROJECT_ROOT / "docs"
CONFIG_DIR = PROJECT_ROOT / "config"
OCTOOLS_DIR = PROJECT_ROOT / "OCTools"

# 资源文件路径
APP_ICON = ASSETS_DIR / "icon.ico"
ALIPAY_QR = ASSETS_DIR / "alipay.png"
WECHAT_QR = ASSETS_DIR / "wechat_pay.png"

# 窗口设置
DEFAULT_WINDOW_WIDTH = 1200
DEFAULT_WINDOW_HEIGHT = 800
MIN_WINDOW_WIDTH = 1000
MIN_WINDOW_HEIGHT = 700
NAVIGATION_WIDTH = 280
MIN_NAVIGATION_EXPAND_WIDTH = 900

# 主题设置
THEME_AUTO = "auto"
THEME_LIGHT = "light"
THEME_DARK = "dark"

# 硬件检测超时时间（秒）
HARDWARE_DETECTION_TIMEOUT = 30

# 系统操作超时时间（秒）
SYSTEM_OPERATION_TIMEOUT = 60

# 支持的文件扩展名
EXECUTABLE_EXTENSIONS = ['.exe', '.msi', '.bat', '.cmd']
IMAGE_EXTENSIONS = ['.png', '.jpg', '.jpeg', '.bmp', '.ico']

# 网络链接
DOUYIN_HOMEPAGE = "https://www.douyin.com"  # 抖音主页链接
QQ_GROUP_LINK = "https://qm.qq.com/cgi-bin/qm/qr"  # QQ群链接
GITHUB_REPO = "https://github.com"  # GitHub仓库链接
UPDATE_CHECK_URL = "https://api.github.com"  # 更新检查URL

# 日志设置
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_DATE_FORMAT = "%Y-%m-%d %H:%M:%S"

# 错误消息
ERROR_MESSAGES = {
    "admin_required": "此操作需要管理员权限",
    "file_not_found": "文件未找到",
    "operation_failed": "操作失败",
    "network_error": "网络连接错误",
    "timeout_error": "操作超时",
    "permission_denied": "权限不足",
    "invalid_parameter": "参数无效"
}

# 成功消息
SUCCESS_MESSAGES = {
    "operation_completed": "操作完成",
    "file_saved": "文件保存成功",
    "settings_updated": "设置更新成功",
    "cleanup_completed": "清理完成",
    "optimization_completed": "优化完成"
}

# 确认消息
CONFIRM_MESSAGES = {
    "delete_file": "确定要删除此文件吗？",
    "reset_settings": "确定要重置所有设置吗？",
    "restart_required": "此操作需要重启系统，是否继续？",
    "dangerous_operation": "这是一个危险操作，可能影响系统稳定性，是否继续？"
}

# PowerShell命令
POWERSHELL_COMMANDS = {
    "get_appx_packages": "Get-AppxPackage",
    "remove_appx_package": "Remove-AppxPackage",
    "get_system_info": "Get-ComputerInfo",
    "restart_explorer": "Stop-Process -Name explorer -Force; Start-Process explorer"
}

# 注册表路径
REGISTRY_PATHS = {
    "current_user": "HKEY_CURRENT_USER",
    "local_machine": "HKEY_LOCAL_MACHINE",
    "taskbar_settings": r"HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced",
    "power_settings": r"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power"
}

# 系统路径
SYSTEM_PATHS = {
    "temp": os.environ.get("TEMP", "C:\\Temp"),
    "windows": os.environ.get("WINDIR", "C:\\Windows"),
    "system32": os.path.join(os.environ.get("WINDIR", "C:\\Windows"), "System32"),
    "program_files": os.environ.get("PROGRAMFILES", "C:\\Program Files"),
    "program_files_x86": os.environ.get("PROGRAMFILES(X86)", "C:\\Program Files (x86)")
}

# 清理目标
CLEANUP_TARGETS = {
    "temp_files": [
        "%TEMP%",
        "%WINDIR%\\Temp",
        "%WINDIR%\\Prefetch"
    ],
    "cache_files": [
        "%LOCALAPPDATA%\\Microsoft\\Windows\\INetCache",
        "%LOCALAPPDATA%\\Microsoft\\Windows\\WebCache",
        "%LOCALAPPDATA%\\Temp"
    ],
    "log_files": [
        "%WINDIR%\\Logs",
        "%WINDIR%\\System32\\LogFiles"
    ]
}

# 图标映射
ICON_MAPPING = {
    "hardware": "💻",
    "optimization": "🧹", 
    "apps": "📱",
    "overclock": "⚡",
    "tools": "🚀",
    "settings": "⚙️",
    "success": "✅",
    "error": "❌",
    "warning": "⚠️",
    "info": "ℹ️"
}

# 颜色定义
COLORS = {
    "primary": "#0078D4",
    "success": "#107C10",
    "warning": "#FF8C00",
    "error": "#D13438",
    "info": "#0078D4"
}

# 动画设置
ANIMATION_DURATION = 250  # 毫秒
FADE_DURATION = 150  # 毫秒
SLIDE_DURATION = 200  # 毫秒

# 性能设置
MAX_CONCURRENT_OPERATIONS = 3
CACHE_EXPIRY_TIME = 300  # 秒
MAX_LOG_FILE_SIZE = 10 * 1024 * 1024  # 10MB

if __name__ == "__main__":
    # 测试常量定义
    print("应用程序常量测试")
    print("-" * 40)
    print(f"应用名称: {APP_NAME}")
    print(f"版本: {APP_VERSION}")
    print(f"作者: {APP_AUTHOR}")
    print(f"项目根目录: {PROJECT_ROOT}")
    print(f"资源目录: {ASSETS_DIR}")
    print(f"默认窗口大小: {DEFAULT_WINDOW_WIDTH}x{DEFAULT_WINDOW_HEIGHT}")
    print("✓ 常量定义正常")

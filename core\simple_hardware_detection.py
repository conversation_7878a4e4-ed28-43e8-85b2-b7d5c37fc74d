#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简化硬件信息检测模块
基于docs/部分思路解析.md中的示例代码实现

此模块使用WMI库来获取硬件信息，
只显示JSON示例中的内容，保持简洁。
"""

import json
import traceback
import datetime
from typing import Dict, Any
from PySide6.QtCore import QThread, Signal

try:
    import wmi
    WMI_AVAILABLE = True
except ImportError:
    WMI_AVAILABLE = False
    print("警告: WMI模块未安装，硬件检测功能将不可用")


def wmi_date_to_str(wmi_date):
    """Converts a WMI CIM_DATETIME to an ISO 8601 string."""
    if wmi_date is None:
        return None
    try:
        # Format is yyyymmddhhmmss.ffffff+|-zzz
        dt = datetime.datetime.strptime(wmi_date.split(".")[0], "%Y%m%d%H%M%S")
        return dt.isoformat()
    except (ValueError, IndexError):
        return str(wmi_date)


def get_system_info():
    """获取操作系统和基本系统信息"""
    try:
        c = wmi.WMI()
        os_info = c.Win32_OperatingSystem()[0]

        return {
            "caption": os_info.Caption,
            "version": os_info.Version,
            "os_architecture": os_info.OSArchitecture,
            "hostname": os_info.CSName,
        }
    except Exception as e:
        return {"error": str(e), "traceback": traceback.format_exc()}


def get_cpu_info():
    """获取CPU详细信息"""
    try:
        c = wmi.WMI()

        processors = []
        for p in c.Win32_Processor():
            processors.append(
                {
                    "device_id": p.DeviceID,
                    "name": p.Name,
                    "manufacturer": p.Manufacturer,
                    "current_clock_speed": f"{p.CurrentClockSpeed} MHz",
                    "max_clock_speed": f"{p.MaxClockSpeed} MHz",
                }
            )

        return {
            "wmi_processor_info": processors,
        }
    except Exception as e:
        return {"error": str(e), "traceback": traceback.format_exc()}


def get_memory_info():
    """获取内存信息"""
    try:
        c = wmi.WMI()
        physical_memory = []

        for mem in c.Win32_PhysicalMemory():
            physical_memory.append(
                {
                    "capacity": f"{int(mem.Capacity) / (1024**3):.2f} GB",
                    "part_number": mem.PartNumber.strip(),
                    "speed": f"{mem.Speed} MHz",
                }
            )

        return {
            "physical_slots": physical_memory,
        }
    except Exception as e:
        return {"error": str(e), "traceback": traceback.format_exc()}


def get_disk_info():
    """获取磁盘信息"""
    try:
        c = wmi.WMI()
        physical_disks = []

        for disk in c.Win32_DiskDrive():
            physical_disks.append(
                {
                    "model": disk.Model,
                    "size": f"{int(disk.Size) / (1024**3):.2f} GB",
                }
            )

        return {
            "physical_disks": physical_disks,
        }
    except Exception as e:
        return {"error": str(e), "traceback": traceback.format_exc()}


def get_gpu_info():
    """获取GPU信息"""
    try:
        c = wmi.WMI()
        gpus = []

        for gpu in c.Win32_VideoController():
            pnp_id = gpu.PNPDeviceID
            if pnp_id and "PCI" in pnp_id:
                gpus.append(
                    {
                        "name": gpu.Name,
                        "pnp_device_id": pnp_id,
                        "driver_version": gpu.DriverVersion,
                        "current_refresh_rate": gpu.CurrentRefreshRate,
                        "current_horizontal_resolution": gpu.CurrentHorizontalResolution,
                        "current_vertical_resolution": gpu.CurrentVerticalResolution,
                    }
                )
        return {"gpus": gpus}
    except Exception as e:
        return {"error": str(e), "traceback": traceback.format_exc()}


def get_motherboard_info():
    """获取主板和BIOS信息"""
    try:
        c = wmi.WMI()
        baseboard = c.Win32_BaseBoard()[0]
        bios = c.Win32_BIOS()[0]
        return {
            "motherboard": {
                "manufacturer": baseboard.Manufacturer,
                "product": baseboard.Product,
            },
            "bios": {
                "manufacturer": bios.Manufacturer,
                "version": bios.SMBIOSBIOSVersion,
                "release_date": wmi_date_to_str(bios.ReleaseDate),
            },
        }
    except Exception as e:
        return {"error": str(e), "traceback": traceback.format_exc()}


def get_all_hardware_info():
    """主函数，聚合所有信息"""
    if not WMI_AVAILABLE:
        return {"error": "WMI模块不可用"}
    
    hardware_info = {
        "detection_time": datetime.datetime.now().isoformat(),
        "system": get_system_info(),
        "cpu": get_cpu_info(),
        "memory": get_memory_info(),
        "disk": get_disk_info(),
        "gpu": get_gpu_info(),
        "motherboard": get_motherboard_info(),
    }
    
    return hardware_info


class SimpleHardwareDetectionThread(QThread):
    """简化硬件检测异步线程"""
    
    # 信号定义
    hardware_detected = Signal(dict)  # 硬件检测完成
    progress_updated = Signal(str, int)  # 进度更新 (状态, 进度百分比)
    error_occurred = Signal(str)  # 错误发生
    
    def __init__(self, parent=None):
        super().__init__(parent)
    
    def run(self):
        """执行硬件检测"""
        try:
            if not WMI_AVAILABLE:
                self.error_occurred.emit("WMI模块不可用，无法检测硬件信息")
                return
            
            self.progress_updated.emit("开始硬件检测...", 0)
            
            self.progress_updated.emit("正在检测系统信息...", 15)
            system_info = get_system_info()
            
            self.progress_updated.emit("正在检测CPU信息...", 30)
            cpu_info = get_cpu_info()
            
            self.progress_updated.emit("正在检测内存信息...", 45)
            memory_info = get_memory_info()
            
            self.progress_updated.emit("正在检测存储信息...", 60)
            disk_info = get_disk_info()
            
            self.progress_updated.emit("正在检测显卡信息...", 75)
            gpu_info = get_gpu_info()
            
            self.progress_updated.emit("正在检测主板信息...", 90)
            motherboard_info = get_motherboard_info()
            
            self.progress_updated.emit("整理检测结果...", 95)
            
            # 组装完整的硬件信息
            hardware_data = {
                "detection_time": datetime.datetime.now().isoformat(),
                "system": system_info,
                "cpu": cpu_info,
                "memory": memory_info,
                "disk": disk_info,
                "gpu": gpu_info,
                "motherboard": motherboard_info
            }
            
            self.progress_updated.emit("硬件检测完成", 100)
            self.hardware_detected.emit(hardware_data)
            
        except Exception as e:
            error_msg = f"硬件检测失败: {str(e)}"
            self.error_occurred.emit(error_msg)


if __name__ == "__main__":
    # 测试代码
    print("简化硬件检测模块测试")
    print("-" * 50)
    
    if not WMI_AVAILABLE:
        print("❌ WMI模块不可用")
        exit(1)
    
    print("检测系统信息...")
    system_info = get_system_info()
    print(f"系统: {system_info.get('caption', '未知')}")
    
    print("\n检测CPU信息...")
    cpu_info = get_cpu_info()
    if 'wmi_processor_info' in cpu_info and cpu_info['wmi_processor_info']:
        print(f"CPU: {cpu_info['wmi_processor_info'][0].get('name', '未知')}")
    
    print("\n检测内存信息...")
    memory_info = get_memory_info()
    if 'physical_slots' in memory_info:
        total_memory = sum(float(slot['capacity'].split()[0]) for slot in memory_info['physical_slots'] if 'capacity' in slot)
        print(f"内存: {total_memory:.2f} GB")
    
    print("\n✓ 简化硬件检测模块测试完成")

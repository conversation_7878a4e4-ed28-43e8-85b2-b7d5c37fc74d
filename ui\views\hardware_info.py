#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
硬件信息视图模块
显示计算机硬件详细信息

提供完整的硬件信息检测、展示、复制和导出功能
支持异步检测，避免界面卡顿
"""

import os
import json
from typing import Dict, Any, Optional
from PySide6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QPushButton,
    QProgressBar,
    QScrollArea,
    QGridLayout,
    QMessageBox,
    QFileDialog,
    QFrame,
)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont

from core.simple_hardware_detection import SimpleHardwareDetectionThread
from ui.components.hardware_card import HardwareCard


class HardwareInfoView(QWidget):
    """硬件信息视图类"""

    def __init__(self, parent=None):
        super().__init__(parent=parent)
        self.setObjectName("HardwareInfoView")

        # 硬件检测线程
        self.detection_thread: Optional[SimpleHardwareDetectionThread] = None

        # 硬件数据
        self.hardware_data: Dict[str, Any] = {}

        # 硬件卡片
        self.hardware_cards: Dict[str, HardwareCard] = {}

        self.init_ui()

        # 自动开始检测
        QTimer.singleShot(500, self.start_detection)

    def init_ui(self):
        """初始化用户界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # 标题栏
        header_layout = self.create_header()
        main_layout.addLayout(header_layout)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet(
            """
            QProgressBar {
                border: 1px solid #D1D1D1;
                border-radius: 4px;
                text-align: center;
                background-color: #F3F2F1;
            }
            QProgressBar::chunk {
                background-color: #0078D4;
                border-radius: 3px;
            }
        """
        )
        main_layout.addWidget(self.progress_bar)

        # 状态标签
        self.status_label = QLabel("准备检测硬件信息...")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setStyleSheet("color: #666666; font-size: 12px; margin: 5px;")
        main_layout.addWidget(self.status_label)

        # 硬件卡片容器
        self.cards_container = self.create_cards_container()
        main_layout.addWidget(self.cards_container, 1)

        # 操作按钮栏
        button_layout = self.create_button_bar()
        main_layout.addLayout(button_layout)

    def create_header(self) -> QHBoxLayout:
        """创建标题栏"""
        header_layout = QHBoxLayout()

        # 标题
        title = QLabel("💻 硬件信息")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title.setFont(title_font)
        title.setStyleSheet("color: #333333; margin-bottom: 5px;")

        # 描述
        description = QLabel("查看您计算机的详细硬件配置信息")
        description.setStyleSheet("color: #666666; font-size: 14px;")

        # 刷新按钮
        self.refresh_button = QPushButton("🔄 重新检测")
        self.refresh_button.setStyleSheet(
            """
            QPushButton {
                background-color: #0078D4;
                border: 1px solid #0078D4;
                border-radius: 4px;
                padding: 8px 16px;
                color: white;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106EBE;
                border-color: #106EBE;
            }
            QPushButton:pressed {
                background-color: #005A9E;
            }
            QPushButton:disabled {
                background-color: #CCCCCC;
                border-color: #CCCCCC;
                color: #666666;
            }
        """
        )
        self.refresh_button.clicked.connect(self.start_detection)

        # 布局
        title_layout = QVBoxLayout()
        title_layout.setSpacing(2)
        title_layout.addWidget(title)
        title_layout.addWidget(description)

        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        header_layout.addWidget(self.refresh_button)

        return header_layout

    def create_cards_container(self) -> QScrollArea:
        """创建硬件卡片容器"""
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameStyle(QFrame.Shape.NoFrame)
        scroll_area.setStyleSheet(
            """
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                border: none;
                background: #F0F0F0;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background: #C0C0C0;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: #A0A0A0;
            }
        """
        )

        # 卡片容器
        self.cards_widget = QWidget()
        self.cards_layout = QGridLayout(self.cards_widget)
        self.cards_layout.setSpacing(15)
        self.cards_layout.setContentsMargins(0, 0, 0, 0)

        # 初始提示
        self.empty_label = QLabel("正在检测硬件信息，请稍候...")
        self.empty_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.empty_label.setStyleSheet(
            """
            QLabel {
                color: #666666;
                font-size: 14px;
                padding: 40px;
                border: 2px dashed #CCCCCC;
                border-radius: 8px;
                background-color: #FAFAFA;
            }
        """
        )
        self.cards_layout.addWidget(self.empty_label, 0, 0, 1, 2)

        scroll_area.setWidget(self.cards_widget)
        return scroll_area

    def create_button_bar(self) -> QHBoxLayout:
        """创建操作按钮栏"""
        button_layout = QHBoxLayout()

        # 全部复制按钮
        self.copy_all_button = QPushButton("📋 复制全部信息")
        self.copy_all_button.setEnabled(False)
        self.copy_all_button.clicked.connect(self.copy_all_info)

        # 导出全部按钮
        self.export_all_button = QPushButton("💾 导出全部数据")
        self.export_all_button.setEnabled(False)
        self.export_all_button.clicked.connect(self.export_all_info)

        # 按钮样式
        button_style = """
            QPushButton {
                background-color: #F3F2F1;
                border: 1px solid #D1D1D1;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #E1DFDD;
                border-color: #0078D4;
            }
            QPushButton:pressed {
                background-color: #D1CFCD;
            }
            QPushButton:disabled {
                background-color: #F8F8F8;
                border-color: #E0E0E0;
                color: #CCCCCC;
            }
        """

        self.copy_all_button.setStyleSheet(button_style)
        self.export_all_button.setStyleSheet(button_style)

        button_layout.addStretch()
        button_layout.addWidget(self.copy_all_button)
        button_layout.addWidget(self.export_all_button)

        return button_layout

    def start_detection(self):
        """开始硬件检测"""
        if self.detection_thread and self.detection_thread.isRunning():
            return  # 如果已在检测中，直接返回

        # 重置界面
        self.clear_cards()
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("正在检测硬件信息...")
        self.refresh_button.setEnabled(False)
        self.copy_all_button.setEnabled(False)
        self.export_all_button.setEnabled(False)

        # 创建并启动检测线程
        self.detection_thread = SimpleHardwareDetectionThread(self)
        self.detection_thread.hardware_detected.connect(self.on_hardware_detected)
        self.detection_thread.progress_updated.connect(self.on_progress_updated)
        self.detection_thread.error_occurred.connect(self.on_error_occurred)
        self.detection_thread.finished.connect(self.on_detection_finished)

        self.detection_thread.start()

    def on_progress_updated(self, status: str, progress: int):
        """进度更新处理"""
        self.status_label.setText(status)
        self.progress_bar.setValue(progress)

    def on_hardware_detected(self, hardware_data: Dict[str, Any]):
        """硬件检测完成处理"""
        self.hardware_data = hardware_data
        self.create_hardware_cards()

        # 启用操作按钮
        self.copy_all_button.setEnabled(True)
        self.export_all_button.setEnabled(True)

    def on_error_occurred(self, error_message: str):
        """错误处理"""
        self.status_label.setText(f"检测失败: {error_message}")
        QMessageBox.warning(self, "硬件检测失败", error_message)

    def on_detection_finished(self):
        """检测完成处理"""
        self.progress_bar.setVisible(False)
        self.refresh_button.setEnabled(True)

        if not self.hardware_data:
            self.status_label.setText("硬件检测失败，请重试")
        else:
            self.status_label.setText(
                f"硬件检测完成 - {self.hardware_data.get('detection_time', '')}"
            )

    def clear_cards(self):
        """清空硬件卡片"""
        # 清除现有卡片
        for card in self.hardware_cards.values():
            card.setParent(None)
            card.deleteLater()
        self.hardware_cards.clear()

        # 显示空状态
        self.empty_label.setText("正在检测硬件信息，请稍候...")
        self.empty_label.setVisible(True)

    def create_hardware_cards(self):
        """创建硬件信息卡片"""
        if not self.hardware_data:
            return

        # 隐藏空状态标签
        self.empty_label.setVisible(False)

        # 硬件类型映射
        hardware_types = [
            ("system", "🖥️ 系统信息", self.hardware_data.get("system", {})),
            ("cpu", "🔧 处理器", self.hardware_data.get("cpu", {})),
            ("memory", "💾 内存", self.hardware_data.get("memory", {})),
            ("gpu", "🎮 显卡", self.hardware_data.get("gpu", {})),
            ("disk", "💿 存储", self.hardware_data.get("disk", {})),
            ("motherboard", "🔌 主板", self.hardware_data.get("motherboard", {})),
        ]

        # 创建卡片
        row, col = 0, 0
        for hw_type, title, data in hardware_types:
            if data and not data.get("error"):  # 只显示有效数据
                card = HardwareCard(
                    title.split(" ", 1)[1], title.split(" ", 1)[0], data, self
                )
                card.copy_requested.connect(self.show_status_message)
                card.export_requested.connect(self.on_card_exported)

                self.hardware_cards[hw_type] = card
                self.cards_layout.addWidget(card, row, col)

                col += 1
                if col >= 2:  # 每行2个卡片
                    col = 0
                    row += 1

    def show_status_message(self, message: str):
        """显示状态消息"""
        self.status_label.setText(message)
        # 3秒后恢复原状态
        QTimer.singleShot(
            3000,
            lambda: self.status_label.setText(
                f"硬件检测完成 - {self.hardware_data.get('detection_time', '')}"
            ),
        )

    def on_card_exported(self, message: str, data: Dict[str, Any]):
        """卡片导出完成处理"""
        self.show_status_message(message)

    def copy_all_info(self):
        """复制所有硬件信息"""
        if not self.hardware_data:
            return

        try:
            # 格式化所有硬件信息
            text_lines = ["💻 Windows硬件工具箱 - 完整硬件信息", "=" * 60]
            text_lines.append(
                f"检测时间: {self.hardware_data.get('detection_time', '未知')}"
            )
            text_lines.append("")

            for hw_type, title, data in [
                ("system", "🖥️ 系统信息", self.hardware_data.get("system", {})),
                ("cpu", "🔧 处理器", self.hardware_data.get("cpu", {})),
                ("memory", "💾 内存", self.hardware_data.get("memory", {})),
                ("gpu", "🎮 显卡", self.hardware_data.get("gpu", {})),
                ("disk", "💿 存储", self.hardware_data.get("disk", {})),
                ("motherboard", "🔌 主板", self.hardware_data.get("motherboard", {})),
            ]:
                if data and not data.get("error"):
                    text_lines.append(f"\n{title}")
                    text_lines.append("-" * 40)
                    if hw_type in self.hardware_cards:
                        card_text = self.hardware_cards[hw_type].format_data_as_text(
                            data
                        )
                        text_lines.append(card_text)

            # 复制到剪贴板
            from PySide6.QtWidgets import QApplication

            clipboard = QApplication.clipboard()
            clipboard.setText("\n".join(text_lines))

            self.show_status_message("所有硬件信息已复制到剪贴板")

        except Exception as e:
            QMessageBox.warning(self, "复制失败", f"复制信息时发生错误：{str(e)}")

    def export_all_info(self):
        """导出所有硬件信息"""
        if not self.hardware_data:
            return

        try:
            # 选择保存文件
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "导出完整硬件信息",
                "hardware_info_complete.json",
                "JSON文件 (*.json);;文本文件 (*.txt)",
            )

            if file_path:
                if file_path.endswith(".json"):
                    # 导出为JSON
                    export_data = {
                        "title": "Windows硬件工具箱 - 完整硬件信息",
                        "export_time": __import__("datetime")
                        .datetime.now()
                        .isoformat(),
                        "detection_time": self.hardware_data.get("detection_time"),
                        "hardware_data": self.hardware_data,
                    }
                    with open(file_path, "w", encoding="utf-8") as f:
                        json.dump(export_data, f, indent=4, ensure_ascii=False)
                else:
                    # 导出为文本
                    self.copy_all_info()  # 先复制到剪贴板
                    clipboard = QApplication.clipboard()
                    text_content = clipboard.text()
                    with open(file_path, "w", encoding="utf-8") as f:
                        f.write(text_content)

                self.show_status_message(
                    f"完整硬件信息已导出到 {os.path.basename(file_path)}"
                )

        except Exception as e:
            QMessageBox.warning(self, "导出失败", f"导出信息时发生错误：{str(e)}")

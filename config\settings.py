#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
应用程序设置模块
管理应用程序的配置和设置
"""

import os
import json
from typing import Dict, Any, Optional
from pathlib import Path


class AppSettings:
    """应用程序设置管理类"""
    
    def __init__(self):
        """初始化设置管理器"""
        self.settings_file = self._get_settings_file_path()
        self.default_settings = self._get_default_settings()
        self.current_settings = self._load_settings()
    
    def _get_settings_file_path(self) -> Path:
        """获取设置文件路径"""
        # 获取用户数据目录
        app_data = os.getenv('APPDATA', os.path.expanduser('~'))
        settings_dir = Path(app_data) / "Windows硬件工具箱"
        settings_dir.mkdir(exist_ok=True)
        return settings_dir / "settings.json"
    
    def _get_default_settings(self) -> Dict[str, Any]:
        """获取默认设置"""
        return {
            "app": {
                "version": "1.0.0",
                "first_run": True,
                "auto_start": False,
                "minimize_to_tray": False,
                "check_updates": True
            },
            "theme": {
                "mode": "auto",  # auto, light, dark
                "follow_system": True,
                "accent_color": "default"
            },
            "hardware": {
                "auto_detect_on_startup": True,
                "cache_hardware_info": True,
                "detection_timeout": 30
            },
            "optimization": {
                "confirm_before_execute": True,
                "create_restore_point": True,
                "show_advanced_options": False
            },
            "tools": {
                "octools_path": "./OCTools",
                "auto_scan_tools": True,
                "show_console_tools": True
            },
            "ui": {
                "window_width": 1200,
                "window_height": 800,
                "navigation_width": 280,
                "remember_window_state": True
            }
        }
    
    def _load_settings(self) -> Dict[str, Any]:
        """加载设置"""
        try:
            if self.settings_file.exists():
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)
                # 合并默认设置和加载的设置
                return self._merge_settings(self.default_settings, loaded_settings)
            else:
                return self.default_settings.copy()
        except Exception as e:
            print(f"加载设置失败: {e}")
            return self.default_settings.copy()
    
    def _merge_settings(self, default: Dict[str, Any], loaded: Dict[str, Any]) -> Dict[str, Any]:
        """合并设置，确保所有默认键都存在"""
        result = default.copy()
        for key, value in loaded.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_settings(result[key], value)
            else:
                result[key] = value
        return result
    
    def save_settings(self) -> bool:
        """保存设置"""
        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.current_settings, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存设置失败: {e}")
            return False
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """获取设置值"""
        try:
            keys = key_path.split('.')
            value = self.current_settings
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any) -> bool:
        """设置值"""
        try:
            keys = key_path.split('.')
            current = self.current_settings
            for key in keys[:-1]:
                if key not in current:
                    current[key] = {}
                current = current[key]
            current[keys[-1]] = value
            return True
        except Exception as e:
            print(f"设置值失败: {e}")
            return False
    
    def reset_to_default(self) -> None:
        """重置为默认设置"""
        self.current_settings = self.default_settings.copy()
    
    def get_all_settings(self) -> Dict[str, Any]:
        """获取所有设置"""
        return self.current_settings.copy()


# 全局设置实例
app_settings = AppSettings()


def get_setting(key_path: str, default: Any = None) -> Any:
    """获取设置值的便捷函数"""
    return app_settings.get(key_path, default)


def set_setting(key_path: str, value: Any) -> bool:
    """设置值的便捷函数"""
    return app_settings.set(key_path, value)


def save_settings() -> bool:
    """保存设置的便捷函数"""
    return app_settings.save_settings()


if __name__ == "__main__":
    # 测试代码
    print("应用程序设置测试")
    print("-" * 40)
    
    # 测试获取设置
    print(f"主题模式: {get_setting('theme.mode')}")
    print(f"窗口宽度: {get_setting('ui.window_width')}")
    print(f"自动检测硬件: {get_setting('hardware.auto_detect_on_startup')}")
    
    # 测试设置值
    set_setting('theme.mode', 'dark')
    print(f"设置后主题模式: {get_setting('theme.mode')}")
    
    # 保存设置
    if save_settings():
        print("✓ 设置保存成功")
    else:
        print("✗ 设置保存失败")

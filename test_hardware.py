#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
硬件信息模块测试
测试硬件检测和界面显示功能
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.admin_check import require_admin
from ui.views.hardware_info import HardwareInfoView


class HardwareTestWindow(QMainWindow):
    """硬件信息测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("Windows硬件工具箱 - 硬件信息测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 添加硬件信息视图
        self.hardware_view = HardwareInfoView(self)
        layout.addWidget(self.hardware_view)


def main():
    """主函数"""
    try:
        # 检查管理员权限
        print("检查管理员权限...")
        require_admin()
        print("✅ 管理员权限检查通过")
        
        # 创建应用程序
        print("初始化应用程序...")
        app = QApplication(sys.argv)
        app.setApplicationName("Windows硬件工具箱")
        app.setApplicationVersion("1.0.0")
        
        # 设置字体
        font = QFont("Segoe UI", 9)
        app.setFont(font)
        print("✅ 应用程序初始化完成")
        
        # 创建测试窗口
        print("创建硬件信息测试窗口...")
        window = HardwareTestWindow()
        window.show()
        print("✅ 硬件信息测试窗口创建成功")
        
        print("🚀 硬件信息模块测试启动成功！")
        
        # 运行应用程序
        sys.exit(app.exec())
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
        sys.exit(0)
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
